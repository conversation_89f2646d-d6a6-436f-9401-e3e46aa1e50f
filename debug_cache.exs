#!/usr/bin/env elixir

# Debug script to test cache functionality
Mix.install([{:jason, "~> 1.4"}])

defmodule TestRepo do
  def config do
    [priv: "test/fixtures/test_repo"]
  end
end

# Create test directories
File.mkdir_p!("test/fixtures/test_repo/migrations")
File.write!("test/fixtures/test_repo/migrations/001_create_users.exs", "# migration 1")

# Enable cache
Application.put_env(:drops, :schema_cache, enabled: true)

# Test basic functionality
IO.puts("=== Testing Cache Functionality ===")

# Test 1: Cache a simple value
IO.puts("\n1. Testing cache_schema...")
result = Drops.Relation.SchemaCache.cache_schema(TestRepo, "users", :test_value)
IO.puts("Cache result: #{inspect(result)}")

# Test 2: Get cached value
IO.puts("\n2. Testing get_cached_schema...")
cached = Drops.Relation.SchemaCache.get_cached_schema(TestRepo, "users")
IO.puts("Cached value: #{inspect(cached)}")

# Test 3: Check if cache is enabled
IO.puts("\n3. Testing cache_enabled?...")
enabled = Drops.Relation.SchemaCache.enabled?()
IO.puts("Cache enabled: #{inspect(enabled)}")

# Test 4: Check cache file path
IO.puts("\n4. Checking cache file path...")
cache_file = Path.join([
  File.cwd!(),
  "tmp/cache/dev/drops_schema",
  "testrepo",
  "users.json"
])
IO.puts("Expected cache file: #{cache_file}")
IO.puts("File exists: #{File.exists?(cache_file)}")

if File.exists?(cache_file) do
  content = File.read!(cache_file)
  IO.puts("File content: #{content}")
end

# Clean up
File.rm_rf!("test/fixtures")
File.rm_rf!("tmp/cache")

IO.puts("\n=== Debug Complete ===")
