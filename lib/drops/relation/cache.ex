defmodule Drops.Relation.Cache do
  @moduledoc """
  Convenience module for managing Drops.Relation schema cache.

  This module provides a simplified interface for common cache operations
  without needing to interact with the SchemaCache GenServer directly.

  ## Examples

      # Clear cache for a specific repository
      Drops.Relation.Cache.clear(MyApp.Repo)

      # Clear all cached schemas
      Drops.Relation.Cache.clear_all()

      # Get cache statistics
      stats = Drops.Relation.Cache.stats()

      # Check if cache is enabled
      if Drops.Relation.Cache.enabled?() do
        # Cache-specific logic
      end

      # Warm up cache for specific tables
      Drops.Relation.Cache.warm_up(MyApp.Repo, ["users", "posts", "comments"])
  """

  alias Drops.Config
  alias Drops.Relation.SchemaCache
  alias Drops.Relation.Schema

  @spec maybe_get_cached_schema(module(), String.t()) :: Schema.t()
  def maybe_get_cached_schema(repo, name) do
    case SchemaCache.get_cached_schema(repo, name) do
      nil -> Schema.empty(name)
      schema -> schema
    end
  end

  @doc """
  Clears all cached schemas for a specific repository.

  ## Parameters

  - `repo` - The Ecto repository module to clear cache for

  ## Examples

      Drops.Relation.Cache.clear(MyApp.Repo)
  """
  @spec clear(module()) :: :ok
  def clear(repo) when is_atom(repo) do
    SchemaCache.clear_repo_cache(repo)
  end

  @doc """
  Clears the entire schema cache for all repositories.

  ## Examples

      Drops.Relation.Cache.clear_all()
  """
  @spec clear_all() :: :ok
  def clear_all do
    SchemaCache.clear_all()
  end

  @doc """
  Checks if schema caching is enabled.

  ## Returns

  Returns `true` if caching is enabled, `false` otherwise.

  ## Examples

      if Drops.Relation.Cache.enabled?() do
        IO.puts("Cache is enabled")
      end
  """
  @spec enabled?() :: boolean()
  def enabled? do
    Config.schema_cache()[:enabled]
  end

  @doc """
  Returns the current cache configuration.

  ## Returns

  A keyword list with the current cache configuration.

  ## Examples

      config = Drops.Relation.Cache.config()
      # => [enabled: true]
  """
  @spec config() :: keyword()
  def config do
    Config.schema_cache()
  end

  @doc """
  Warms up the cache by pre-loading schemas for specified tables.

  This function infers schemas for the given tables and caches the results.
  This can be useful during application startup to ensure frequently used
  schemas are cached.

  ## Parameters

  - `repo` - The Ecto repository module
  - `table_names` - List of table names to warm up

  ## Returns

  Returns `:ok` on success, or `{:error, reason}` if warming up fails.

  ## Examples

      # Warm up cache for common tables
      Drops.Relation.Cache.warm_up(MyApp.Repo, ["users", "posts", "comments"])
  """
  @spec warm_up(module(), [String.t()]) :: :ok | {:error, term()}
  def warm_up(repo, table_names) when is_atom(repo) and is_list(table_names) do
    if enabled?() do
      try do
        Enum.each(table_names, fn table_name ->
          # Check if already cached
          unless SchemaCache.get_cached_schema(repo, table_name) do
            # Infer and cache the schema
            schema = Drops.Relation.SQL.Inference.infer_from_table(table_name, repo)
            SchemaCache.cache_schema(repo, table_name, schema)
          end
        end)

        :ok
      rescue
        error -> {:error, error}
      end
    else
      {:error, :cache_disabled}
    end
  end

  @doc """
  Forces a refresh of cached schemas for a repository.

  This clears the cache for the repository and then optionally warms it up
  again with the specified table names.

  ## Parameters

  - `repo` - The Ecto repository module
  - `table_names` - Optional list of table names to warm up after clearing

  ## Examples

      # Just clear the cache
      Drops.Relation.Cache.refresh(MyApp.Repo)

      # Clear and warm up specific tables
      Drops.Relation.Cache.refresh(MyApp.Repo, ["users", "posts"])
  """
  @spec refresh(module(), [String.t()] | nil) :: :ok | {:error, term()}
  def refresh(repo, table_names \\ nil) when is_atom(repo) do
    clear(repo)

    case table_names do
      nil -> :ok
      names when is_list(names) -> warm_up(repo, names)
    end
  end
end
