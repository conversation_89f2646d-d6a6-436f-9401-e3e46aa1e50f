defmodule Drops.Relation.SchemaCacheTest do
  use ExUnit.Case, async: false

  alias Drops.Relation.SchemaCache
  alias Drops.Config

  # Mock repository for testing
  defmodule TestRepo do
    def config do
      [priv: "test/fixtures/test_repo"]
    end
  end

  # Another mock repo with different migration directory
  defmodule TestRepo2 do
    def config do
      [priv: "test/fixtures/test_repo2"]
    end
  end

  # Mock repo with no migrations
  defmodule EmptyRepo do
    def config do
      [priv: "test/fixtures/empty_repo"]
    end
  end

  setup do
    # Clear cache before each test
    SchemaCache.clear_all()

    # Mock config to enable cache
    original_config = Config.schema_cache()

    on_exit(fn ->
      # Restore original config
      Config.update(:schema_cache, original_config)
    end)

    # Enable cache for tests
    Config.update(:schema_cache, enabled: true)

    # Create test fixture directories
    File.mkdir_p!("test/fixtures/test_repo/migrations")
    File.mkdir_p!("test/fixtures/test_repo2/migrations")
    File.mkdir_p!("test/fixtures/empty_repo")

    # Create test migration files
    File.write!(
      "test/fixtures/test_repo/migrations/001_create_users.exs",
      "# migration 1"
    )

    File.write!(
      "test/fixtures/test_repo2/migrations/001_create_posts.exs",
      "# migration 2"
    )

    on_exit(fn ->
      File.rm_rf!("test/fixtures")
    end)

    :ok
  end

  describe "get_cached_schema/2" do
    test "returns cached schema on cache hit" do
      # Cache a schema first
      SchemaCache.cache_schema(TestRepo, "users", :mock_drops_schema)

      # Should return cached schema
      result = SchemaCache.get_cached_schema(TestRepo, "users")
      assert result == :mock_drops_schema
    end

    test "returns nil on cache miss" do
      result = SchemaCache.get_cached_schema(TestRepo, "posts")
      assert result == nil
    end

    test "invalidates cache when migration digest changes" do
      # Cache a schema first
      SchemaCache.cache_schema(TestRepo, "users", :drops_schema_v1)

      # Should return cached schema
      result1 = SchemaCache.get_cached_schema(TestRepo, "users")
      assert result1 == :drops_schema_v1

      # Modify migration file to change digest
      File.write!(
        "test/fixtures/test_repo/migrations/001_create_users.exs",
        "# modified migration 1"
      )

      # Should return nil due to digest mismatch
      result2 = SchemaCache.get_cached_schema(TestRepo, "users")
      assert result2 == nil
    end

    test "handles repository with no migrations" do
      # Cache a schema for empty repo
      SchemaCache.cache_schema(EmptyRepo, "users", :empty_drops_schema)

      result = SchemaCache.get_cached_schema(EmptyRepo, "users")
      assert result == :empty_drops_schema
    end

    test "returns nil when cache is disabled" do
      # Cache a schema first
      SchemaCache.cache_schema(TestRepo, "users", :cached_schema)

      # Disable cache
      Config.update(:schema_cache, enabled: false)

      # Should return nil when cache is disabled
      result = SchemaCache.get_cached_schema(TestRepo, "users")
      assert result == nil
    end
  end

  describe "cache_schema/3" do
    test "caches schema successfully" do
      SchemaCache.cache_schema(TestRepo, "users", :test_schema)

      result = SchemaCache.get_cached_schema(TestRepo, "users")
      assert result == :test_schema
    end

    test "does nothing when cache is disabled" do
      Config.update(:schema_cache, enabled: false)

      SchemaCache.cache_schema(TestRepo, "users", :test_schema)

      # Re-enable cache to check if anything was cached
      Config.update(:schema_cache, enabled: true)
      result = SchemaCache.get_cached_schema(TestRepo, "users")
      assert result == nil
    end
  end

  describe "clear_repo_cache/1" do
    test "clears cache for specific repository" do
      # Cache schemas for both repos
      SchemaCache.cache_schema(TestRepo, "users", :drops_schema)
      SchemaCache.cache_schema(TestRepo2, "users", :drops_schema)

      # Verify both are cached
      assert SchemaCache.get_cached_schema(TestRepo, "users") == :drops_schema
      assert SchemaCache.get_cached_schema(TestRepo2, "users") == :drops_schema

      # Clear cache for TestRepo only
      SchemaCache.clear_repo_cache(TestRepo)

      # TestRepo should be cleared, TestRepo2 should still be cached
      assert SchemaCache.get_cached_schema(TestRepo, "users") == nil
      assert SchemaCache.get_cached_schema(TestRepo2, "users") == :drops_schema
    end
  end

  describe "clear_all/0" do
    test "clears entire cache" do
      # Cache multiple schemas
      SchemaCache.cache_schema(TestRepo, "users", :drops_schema)
      SchemaCache.cache_schema(TestRepo, "posts", :drops_schema)
      SchemaCache.cache_schema(TestRepo2, "users", :drops_schema)

      # Verify all are cached
      assert SchemaCache.get_cached_schema(TestRepo, "users") == :drops_schema
      assert SchemaCache.get_cached_schema(TestRepo, "posts") == :drops_schema
      assert SchemaCache.get_cached_schema(TestRepo2, "users") == :drops_schema

      # Clear all
      SchemaCache.clear_all()

      # All should be cleared
      assert SchemaCache.get_cached_schema(TestRepo, "users") == nil
      assert SchemaCache.get_cached_schema(TestRepo, "posts") == nil
      assert SchemaCache.get_cached_schema(TestRepo2, "users") == nil
    end
  end
end

defmodule Drops.Relation.CacheTest do
  use ExUnit.Case, async: false

  alias Drops.Relation.SchemaCache
  alias Drops.Config

  # Use the same mock repos from the previous test
  alias Drops.Relation.SchemaCacheTest.{TestRepo, TestRepo2}

  setup do
    # Clear cache before each test
    SchemaCache.clear_all()

    # Enable cache for tests
    original_config = Config.schema_cache()

    on_exit(fn ->
      Config.update(:schema_cache, original_config)
      File.rm_rf!("test/fixtures")
    end)

    Config.update(:schema_cache, enabled: true)

    # Create test fixture directories
    File.mkdir_p!("test/fixtures/test_repo/migrations")
    File.mkdir_p!("test/fixtures/test_repo2/migrations")

    # Create test migration files
    File.write!(
      "test/fixtures/test_repo/migrations/001_create_users.exs",
      "# migration 1"
    )

    File.write!(
      "test/fixtures/test_repo2/migrations/001_create_posts.exs",
      "# migration 2"
    )

    :ok
  end

  describe "clear_repo_cache/1" do
    test "clears cache for specific repository" do
      # Add some cached entries
      SchemaCache.cache_schema(TestRepo, "users", :mock_drops_schema)
      SchemaCache.cache_schema(TestRepo2, "users", :mock_drops_schema)

      # Verify both are cached
      assert SchemaCache.get_cached_schema(TestRepo, "users") == :mock_drops_schema
      assert SchemaCache.get_cached_schema(TestRepo2, "users") == :mock_drops_schema

      # Clear cache for TestRepo
      SchemaCache.clear_repo_cache(TestRepo)

      # TestRepo should be cleared, TestRepo2 should still be cached
      assert SchemaCache.get_cached_schema(TestRepo, "users") == nil
      assert SchemaCache.get_cached_schema(TestRepo2, "users") == :mock_drops_schema
    end
  end

  describe "clear_all/0" do
    test "clears entire cache" do
      SchemaCache.cache_schema(TestRepo, "users", :mock_drops_schema)
      SchemaCache.cache_schema(TestRepo, "posts", :mock_drops_schema)

      # Verify both are cached
      assert SchemaCache.get_cached_schema(TestRepo, "users") == :mock_drops_schema
      assert SchemaCache.get_cached_schema(TestRepo, "posts") == :mock_drops_schema

      SchemaCache.clear_all()

      # Both should be cleared
      assert SchemaCache.get_cached_schema(TestRepo, "users") == nil
      assert SchemaCache.get_cached_schema(TestRepo, "posts") == nil
    end
  end

  describe "enabled?/0" do
    test "returns true when cache is enabled" do
      Config.update(:schema_cache, enabled: true)
      assert SchemaCache.enabled?() == true
    end

    test "returns false when cache is disabled" do
      Config.update(:schema_cache, enabled: false)
      assert SchemaCache.enabled?() == false
    end
  end

  describe "config/0" do
    test "returns current cache configuration" do
      config = SchemaCache.config()

      assert is_list(config)
      assert Keyword.has_key?(config, :enabled)
    end
  end

  describe "warm_up/2" do
    test "returns error when cache is disabled" do
      Config.update(:schema_cache, enabled: false)

      result = SchemaCache.warm_up(TestRepo, ["users", "posts"])
      assert result == {:error, :cache_disabled}
    end

    test "returns ok when cache is enabled" do
      # This test is limited because we don't have real table introspection
      # In a real scenario, this would require actual database tables
      result = SchemaCache.warm_up(TestRepo, [])
      assert result == :ok
    end
  end

  describe "refresh/2" do
    test "clears and optionally warms up cache" do
      SchemaCache.cache_schema(TestRepo, "users", :mock_drops_schema)

      # Verify it's cached
      assert SchemaCache.get_cached_schema(TestRepo, "users") == :mock_drops_schema

      # Refresh without warming up
      result = SchemaCache.refresh(TestRepo)
      assert result == :ok
      assert SchemaCache.get_cached_schema(TestRepo, "users") == nil

      # Refresh with warming up (empty list for this test)
      result = SchemaCache.refresh(TestRepo, [])
      assert result == :ok
    end
  end
end
