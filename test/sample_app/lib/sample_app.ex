defmodule SampleApp do
  @moduledoc """
  Sample application for testing Drops.Relation schema generation.
  """

  use Application

  def start(_type, _args) do
    children = [
      SampleApp.Repo
    ]

    case Drops.Relation.Cache.warm_up(SampleApp.Repo, ["users"]) do
      :ok ->
        IO.puts("✓ Schema cache populated")

      {:error, :cache_disabled} ->
        IO.puts("- Schema cache disabled")

      {:error, error} ->
        IO.puts("⚠ Failed to populate schema cache: #{inspect(error)}")
    end

    opts = [strategy: :one_for_one, name: SampleApp.Supervisor]

    Supervisor.start_link(children, opts)
  end
end
